// import { z } from "zod"


// const signUpSchema = z.object({
//     firstName: z.string().min(1, {message: "First name is required"}),
//     lastName: z.string().min(1, {message: "Last name is required"}),
//     email: z.string().min(1, {message: "Email is required"}).email(),
//     password: z.string().min(8, {message: "Password must be at least 8 characters"})
//     .regex(/.*[!@#$%^&*()_+{}|[\]\\:";'<>?,./~`].*/, "Password must contain a special character"),
//     confirmPassword: z.string().min(1, {message: "Confirm password is required"}),
//   })
//   .refine((input) => input.password === input.confirmPassword, {
//     message: "Password and confirm password do not match",
//     path: ["confirmPassword"]
//   })
  
  
//   type signUpType = z.infer<typeof signUpSchema>;
  

// export {signUpSchema,  type signUpType}

import { z } from "zod";

const signUpSchema = z
  .object({
    firstName: z.string().min(1, { message: "First name is required" }),
    lastName: z.string().min(1, { message: "Last name is required" }),
    email: z.string().min(1, { message: "Email is required" }).email(),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" })
      .regex(
        /.*[!@#$%^&*()_+{}|[\]\\:";'<>?,./~`].*/,
        "Password must contain a special character"
      ),
    confirmPassword: z
      .string()
      .min(1, { message: "Confirm password is required" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Password and confirm password do not match",
    path: ["confirmPassword"],
  });

type signUpType = z.infer<typeof signUpSchema>;

export { signUpSchema, type signUpType };