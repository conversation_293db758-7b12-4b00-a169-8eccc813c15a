import { Suspense } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./LottieHandler";

type TPageSuspenseFallback = {
  children: React.ReactNode;
};

const PageSuspenseFallback = ({ children }: TPageSuspenseFallback) => {
  return (
    <Suspense
      fallback={
        <div
          style={{
            marginTop: "10%",
          }}
        >
          <LottieHandler type="loading" message="Loading Please Wait..." />
        </div>
      }
    >
      {children}
    </Suspense>
  );
};

export default PageSuspenseFallback;
