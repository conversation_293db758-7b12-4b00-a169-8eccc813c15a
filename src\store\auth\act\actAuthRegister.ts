// import { createAsyncThunk } from "@reduxjs/toolkit";
// import { axiosErrorHandler } from "@utils";
// import axios from "axios";

// type TFormData = {
//     firstName: string,
//     lastName: string,
//     email: string,
//     password: string,
// }

// const actAuthRegister = createAsyncThunk(
//     "auth/actAuthRegister",
//     async(formData: TFormData, thunkAPI) => {
//         const { rejectWithValue } = thunkAPI;
//         try {
//             const response = await axios.post("/api/auth/register", formData);
//             return response.data
//         } catch (error) {
//             return rejectWithValue(axiosErrorHandler(error));
//         }
//     }

// )

// export default actAuthRegister

import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import axiosErrorHandler from "@utils/axiosErrorHandler";

type TFormData = {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
}
const actAuthRegister = createAsyncThunk(
  "auth/actAuthRegister",
  async (formData: TFormData, thunkAPI) => {
    const { rejectWithValue } = thunkAPI;
    try {
      const res = await axios.post("/api/auth/register", formData);
      return res.data;
    } catch (error) {
      return rejectWithValue(axiosErrorHandler(error));
    }
  }
);
export default actAuthRegister;
