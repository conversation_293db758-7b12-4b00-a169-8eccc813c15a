import { createSlice } from "@reduxjs/toolkit";
import { TLoading } from "@types";
import actGetProductsByCatPrefix from "./act/actGetProductsByCatPrefix";

export interface IProduct {
  id: number;
  title: string;
  price: number;
  cat_prefix: string;
  img: string;
  max: number;
  quantity?: number;
  isLiked?: boolean;
  isAuthenticated?: boolean;
}

interface IProductsState {
  records: IProduct[];
  loading: TLoading;
  error: string | null;
}

const initialState: IProductsState = {
  records: [],
  loading: "idle",
  error: null,
};

const productsSlice = createSlice({
  name: "products",
  initialState,
  reducers: {
    cleanUpProductsRecords: (state) => {
      state.records = [];
    },
    productsFullInfoCleanUp: (state) => {
      state.records = state.records.map((el) => ({
        id: el.id,
        title: el.title,
        price: el.price,
        cat_prefix: el.cat_prefix,
        img: el.img,
        max: el.max,
      }));
    },
  },
  extraReducers: (builder) => {
    builder.addCase(actGetProductsByCatPrefix.pending, (state) => {
      state.loading = "pending";
      state.error = null;
    });
    builder.addCase(actGetProductsByCatPrefix.fulfilled, (state, action) => {
      state.loading = "succeeded";
      state.records = action.payload;
    });
    builder.addCase(actGetProductsByCatPrefix.rejected, (state, action) => {
      state.loading = "failed";
      if (action.payload && typeof action.payload === "string") {
        state.error = action.payload;
      }
    });
  },
});

export { actGetProductsByCatPrefix };
export const { cleanUpProductsRecords, productsFullInfoCleanUp } = productsSlice.actions;
export default productsSlice.reducer;
