import Lottie from "lottie-react";
import notFound from "@assets/lottieFiles/notFound.json";
import empty from "@assets/lottieFiles/empty.json";
import loading from "@assets/lottieFiles/loading.json";
import error from "@assets/lottieFiles/error.json";
import success from "@assets/lottieFiles/success.json";

const lottieTypes = {
  loading,
  empty,
  notFound,
  error,
  success,
};

type TLottieHandler = {
  type: keyof typeof lottieTypes;
  message?: string;
  className?: string;
};

const LottieHandler = ({ type, message, className }: TLottieHandler) => {
  const lottie = lottieTypes[type];
  const messageStyle = {
    fontSize: "19px",
    marginTop: "30px",
  };

  return (
    <div className={`d-flex flex-column align-items-center ${className}`}>
      <Lottie animationData={lottie} style={{ width: "400px" }} />
      {message && <h3 style={messageStyle}>{message}</h3>}
    </div>
  );
};

export default LottieHandler;
