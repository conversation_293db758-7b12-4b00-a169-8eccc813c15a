// import { Navigate } from "react-router-dom"
// import useLogin from "@hooks/useLogin"
// import { Input } from "@components/form"
// import { Heading } from "@components/common"
// import { Al<PERSON>, Button, Col, Form, Row, Spinner } from "react-bootstrap"

// const Login = () => {
//   const {loading, error, accessToken, message, register, handleSubmit, formErrors, submitForm} = useLogin();

//   if(accessToken) {
//     return <Navigate to={"/"}/>
//   }

//   return (
//     <>
//     <Heading title="User Login" />
//     <Row>
//       <Col md={{ span: 6, offset: 3}}>
//       {message && <Alert variant="danger">You must login to view this page.</Alert>}
//       {message && message === "account_created" && <Alert variant="success">Your account has been created. Please login.</Alert>}
//         <Form onSubmit={handleSubmit(submitForm)}>
//           <Input name="email" register={register} type="email" error={formErrors.email?.message} label="Email address"/>
//           <Input name="password" register={register} type="password" error={formErrors.password?.message} label="Password" />
//           <Button variant="info" type="submit" style={{color: "white"}}>
//             {loading === "pending" ? ( <> <Spinner animation="border" size="sm"></Spinner> Loading ...</>) : "Submit"}
//           </Button>
//           {error && ( <p style={{color: "red", marginTop: "10px"}}>{error}</p> )}
//         </Form>
//       </Col>
//     </Row>
//     </>
//     )
// }

// export default Login

// import { Heading } from "@components/common"
// import { Button, Col, Form, Row } from "react-bootstrap";

// const Login = () => {
//   return (
//     <>
//       <Heading title="User Login" />
//       <Row>
//         <Col md={{ span: 6, offset: 3}}>
//           <Form>
//             <Form.Group className="mb-3">
//               <Form.Label>Email address</Form.Label>
//               <Form.Control
//                 type="text"
//                 name="email"
//                 placeholder="Enter email"
//               />
//             </Form.Group>
//             <Form.Group className="mb-3">
//               <Form.Label>Password</Form.Label>
//               <Form.Control
//                 type="password"
//                 name="password"
//                 placeholder="Password"
//               />
//             </Form.Group>
//             <Button variant="info" type="submit" style={{ color: "white"}}>
//               Submit
//             </Button>
//           </Form>
//         </Col>
//       </Row>
//     </>
//   );
// }

// export default Login

import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { signInSchema, type signInType } from "@validations/signInSchema";
import { Heading } from "@components/common";
import { Input } from "@components/form";
import { Button, Col, Row } from "react-bootstrap";
import { Form } from "react-bootstrap";
import { useAppDispatch } from "@store/hooks";
import { actAuthLogin } from "@store/auth/authSlice";
import { useNavigate } from "react-router-dom";

const Login = () => {
  const dispatch = useAppDispatch();

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<signInType>({
    resolver: zodResolver(signInSchema),
    mode: "onBlur",
  });
  const onSubmit: SubmitHandler<signInType> = (data) => {
    dispatch(actAuthLogin(data))
      .unwrap()
      .then(() => navigate("/"));
  };
  return (
    <>
      <Heading title="User Login" />
      <Row>
        <Col md={{ span: 6, offset: 3 }}>
          <Form onSubmit={handleSubmit(onSubmit)}>
            <Input
              label="Email address"
              name="email"
              type="email"
              register={register}
              error={errors.email?.message}
              key={"email"}
            />
            <Input
              label="Password"
              name="password"
              type="password"
              register={register}
              error={errors.password?.message}
              key={"password"}
            />
            <Button variant="info" type="submit" style={{ color: "white" }}>
              Submit
            </Button>
          </Form>
        </Col>
      </Row>
    </>
  );
};

export default Login;
