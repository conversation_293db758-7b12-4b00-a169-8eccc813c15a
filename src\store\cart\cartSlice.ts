import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import { IProduct } from "@store/products/productsSlice";
import { TLoading } from "@types";
import actGetProductsByItems from "./act/actGetProductsByItems";

export interface ICartItem {
  id: number;
  quantity: number;
}

export interface ICartFullInfo extends IProduct {
  quantity: number;
}

interface ICartState {
  items: ICartItem[];
  productsFullInfo: ICartFullInfo[];
  loading: TLoading;
  error: string | null;
}

const initialState: ICartState = {
  items: [],
  productsFullInfo: [],
  loading: "idle",
  error: null,
};

const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<number>) => {
      const id = action.payload;
      const isItemInCart = state.items.find((item) => item.id === id);
      if (isItemInCart) {
        isItemInCart.quantity++;
      } else {
        state.items.push({ id, quantity: 1 });
      }
    },
    cartItemChangeQuantity: (state, action: PayloadAction<{ id: number; quantity: number }>) => {
      const { id, quantity } = action.payload;
      const isItemInCart = state.items.find((item) => item.id === id);
      if (isItemInCart) {
        isItemInCart.quantity = quantity;
      }
    },
    cartItemRemove: (state, action: PayloadAction<number>) => {
      state.items = state.items.filter((item) => item.id !== action.payload);
    },
    cartProductsFullInfoCleanUp: (state) => {
      state.productsFullInfo = [];
    },
    clearCartAfterPlaceOrder: (state) => {
      state.items = [];
      state.productsFullInfo = [];
    },
  },
  extraReducers: (builder) => {
    builder.addCase(actGetProductsByItems.pending, (state) => {
      state.loading = "pending";
      state.error = null;
    });
    builder.addCase(actGetProductsByItems.fulfilled, (state, action) => {
      state.loading = "succeeded";
      state.productsFullInfo = action.payload;
    });
    builder.addCase(actGetProductsByItems.rejected, (state, action) => {
      state.loading = "failed";
      if (action.payload && typeof action.payload === "string") {
        state.error = action.payload;
      }
    });
  },
});

export { actGetProductsByItems };
export const {
  addToCart,
  cartItemChangeQuantity,
  cartItemRemove,
  cartProductsFullInfoCleanUp,
  clearCartAfterPlaceOrder,
} = cartSlice.actions;
export default cartSlice.reducer;
