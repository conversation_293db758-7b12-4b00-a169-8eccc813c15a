import { useAppDispatch, useAppSelector } from "@store/hooks";
import { useForm, type SubmitHandler } from "react-hook-form";
import { Form, Button, Row, Col, Spinner } from "react-bootstrap";
import { Heading } from "@components/common";
import { zodResolver } from "@hookform/resolvers/zod";
import { signUpSchema, type signUpType } from "@validations/signUpSchema";
import { Input } from "@components/form";
import type React from "react";
import useCheckEmailAvailability from "@hooks/useCheckEmailAvailability";
import { actAuthRegister } from "@store/auth/authSlice";
import { useNavigate } from "react-router-dom";

const Register = () => {
  const dispatch = useAppDispatch();

  const { error, loading } = useAppSelector((state) => state.auth);

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    getFieldState,
    trigger,
    formState: { errors },
  } = useForm<signUpType>({
    resolver: zodResolver(signUpSchema),
    mode: "onBlur",
  });
  const submitForm: SubmitHandler<signUpType> = async (data) => {
    const { firstName, lastName, email, password } = data;
    dispatch(actAuthRegister({ firstName, lastName, email, password }))
      .unwrap()
      .then(() => navigate("/login"));
  };

  const {
    emailAvailabilityStatus,
    enteredEmail,
    checkEmailAvailability,
    resetCheckEmailAvailability,
  } = useCheckEmailAvailability();
  const emailOnBlurHandler = async (e: React.FocusEvent<HTMLInputElement>) => {
    await trigger("email");
    const { isDirty, invalid } = getFieldState("email");
    const value = e.target.value;
    if (isDirty && !invalid && enteredEmail !== value) {
      checkEmailAvailability(value);
    }
    if (isDirty && invalid && enteredEmail) {
      resetCheckEmailAvailability();
    }
  };
  return (
    <>
      <Heading title="User Registration" />
      <Row>
        <Col md={{ span: 6, offset: 3 }}>
          <Form onSubmit={handleSubmit(submitForm)}>
            <Input
              name="firstName"
              label="First Name"
              register={register}
              type="text"
              error={errors.firstName?.message}
              key={"firstName"}
            />
            <Input
              name="lastName"
              label="Last Name"
              register={register}
              type="text"
              error={errors.lastName?.message}
              key={"lastName"}
            />
            <Input
              name="email"
              label="Email"
              register={register}
              type="text"
              error={
                errors.email?.message
                  ? errors.email?.message
                  : emailAvailabilityStatus === "notAvailable"
                  ? "This email address is already registered"
                  : emailAvailabilityStatus === "failed"
                  ? "Error from the server. Please try again later"
                  : ""
              }
              key={"email"}
              onBlur={emailOnBlurHandler}
              formText={
                emailAvailabilityStatus === "checking"
                  ? "We are checking the availability of email address. Please wait a moment"
                  : ""
              }
              success={
                emailAvailabilityStatus === "available"
                  ? "This email address is available for registration"
                  : undefined
              }
              disabled={emailAvailabilityStatus === "checking"}
            />
            <Input
              name="password"
              label="Password"
              register={register}
              type="password"
              error={errors.password?.message}
              key={"password"}
            />
            <Input
              name="confirmPassword"
              label="Confirm Password"
              register={register}
              type="password"
              error={errors.confirmPassword?.message}
              key={"confirmPassword"}
            />
            <Button
              variant="info"
              type="submit"
              className="mb-3"
              style={{ color: "white" }}
              disabled={
                emailAvailabilityStatus === "checking" || loading === "pending"
              }
            >
              {loading === "pending" ? (
                <>
                  <Spinner animation="border" size="sm"></Spinner> Loading...
                </>
              ) : (
                "Submit"
              )}
            </Button>
            {error && (
              <p style={{ color: "red", marginTop: "10px" }}>{error}</p>
            )}
          </Form>
        </Col>
      </Row>
    </>
  );
};

export default Register;
