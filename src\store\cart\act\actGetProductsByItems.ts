import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import axiosErrorHandler from "@utils/axiosErrorHandler";
import { RootState } from "@store";
import { ICartFullInfo } from "../cartSlice";

const actGetProductsByItems = createAsyncThunk(
  "cart/actGetProductsByItems",
  async (_, thunkAPI) => {
    const { rejectWithValue, getState } = thunkAPI;
    const { cart } = getState() as RootState;
    const itemsId = cart.items.map((el) => el.id);

    if (!itemsId.length) {
      return [];
    }

    try {
      const concatenatedItemsId = itemsId.map((el) => `id=${el}`).join("&");
      const response = await axios.get<ICartFullInfo[]>(`/products?${concatenatedItemsId}`);
      
      return response.data.map((el) => ({
        ...el,
        quantity: cart.items.find((item) => item.id === el.id)?.quantity || 0,
      }));
    } catch (error) {
      return rejectWithValue(axiosErrorHandler(error));
    }
  }
);

export default actGetProductsByItems;
