import { Container } from "react-bootstrap";
import { Outlet } from "react-router-dom";
import { DevTools } from "@components/common";
import styles from "./styles.module.css";
const { container, wrapper } = styles;
const MainLayout = () => {
  return (
    <Container className={container}>
      <div className={wrapper}>
        <Outlet />
      </div>
      <DevTools />
    </Container>
  );
};

export default MainLayout;
